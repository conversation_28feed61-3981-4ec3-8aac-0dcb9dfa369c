CREATE DATABASE QLSV;
GO

-- Sử dụng database
USE QLSV;
GO

-- <PERSON><PERSON><PERSON> bảng SinhVien
CREATE TABLE SinhVien (
    MaSV INT IDENTITY(1,1) PRIMARY KEY,   -- Tự tăng
    HoTen NVARCHAR(100) NOT NULL,
    <PERSON><PERSON> INT NOT NULL,
    GioiTinh NVARCHAR(10) NOT NULL CHECK (GioiTinh IN (N'Nam', N'Nữ'))
);
GO

-- Thê<PERSON> dữ liệu mẫu
INSERT INTO SinhVien (HoTen, Tuoi, GioiTinh) VALUES
(N'Nguyễn <PERSON>', 20, N'Nam'),
(N'Trần Thị Bình', 21, N'N<PERSON>'),
(N'Lê Văn <PERSON>', 19, N'Nam'),
(N'Phạm Thị Dung', 22, N'Nữ');
GO