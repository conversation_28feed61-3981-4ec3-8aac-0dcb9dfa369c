<?xml version="1.0" encoding="UTF-8"?>
<actions>
        <action>
            <actionName>run</actionName>
            <packagings>
                <packaging>jar</packaging>
            </packagings>
            <goals>
                <goal>process-classes</goal>
                <goal>org.codehaus.mojo:exec-maven-plugin:3.1.0:exec</goal>
            </goals>
            <properties>
                <exec.vmArgs></exec.vmArgs>
                <exec.args>${exec.vmArgs} -classpath %classpath ${exec.mainClass} ${exec.appArgs}</exec.args>
                <exec.appArgs></exec.appArgs>
                <exec.mainClass>view.FrmQLSanPham</exec.mainClass>
                <exec.executable>java</exec.executable>
            </properties>
        </action>
        <action>
            <actionName>debug</actionName>
            <packagings>
                <packaging>jar</packaging>
            </packagings>
            <goals>
                <goal>process-classes</goal>
                <goal>org.codehaus.mojo:exec-maven-plugin:3.1.0:exec</goal>
            </goals>
            <properties>
                <exec.vmArgs>-agentlib:jdwp=transport=dt_socket,server=n,address=${jpda.address}</exec.vmArgs>
                <exec.args>${exec.vmArgs} -classpath %classpath ${exec.mainClass} ${exec.appArgs}</exec.args>
                <exec.appArgs></exec.appArgs>
                <exec.mainClass>view.FrmQLSanPham</exec.mainClass>
                <exec.executable>java</exec.executable>
                <jpda.listen>true</jpda.listen>
            </properties>
        </action>
        <action>
            <actionName>profile</actionName>
            <packagings>
                <packaging>jar</packaging>
            </packagings>
            <goals>
                <goal>process-classes</goal>
                <goal>org.codehaus.mojo:exec-maven-plugin:3.1.0:exec</goal>
            </goals>
            <properties>
                <exec.vmArgs></exec.vmArgs>
                <exec.args>${exec.vmArgs} -classpath %classpath ${exec.mainClass} ${exec.appArgs}</exec.args>
                <exec.mainClass>view.FrmQLSanPham</exec.mainClass>
                <exec.executable>java</exec.executable>
                <exec.appArgs></exec.appArgs>
            </properties>
        </action>
    </actions>
